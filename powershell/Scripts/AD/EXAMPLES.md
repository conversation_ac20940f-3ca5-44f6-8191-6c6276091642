# Test-Create-ADObject.ps1 API Examples

This document provides examples for calling the `Test-Create-ADObject.ps1` script through the webhookshell API.

## Configuration

The script is configured in `webhookshell/src/appsettings.json` with:
- **Script Name**: `Test-Create-ADObject.ps1`
- **Key**: `77aae8aa-50d2-49d9-be8c-e9f59aaf39e9`
- **Supported Methods**: GET, POST

## GET Request Examples

### Basic Windows VM Test
```
https://localhost:5001/webhook/v1?key=77aae8aa-50d2-49d9-be8c-e9f59aaf39e9&script=Test-Create-ADObject.ps1&params=-jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"
```

### Cluster Test
```
https://localhost:5001/webhook/v1?key=77aae8aa-50d2-49d9-be8c-e9f59aaf39e9&script=Test-Create-ADObject.ps1&params=-jobId "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -domain "internal.com" -ouPath "OU=Clusters,DC=internal,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"
```

### Linux VM Test (Should Skip AD Creation)
```
https://localhost:5001/webhook/v1?key=77aae8aa-50d2-49d9-be8c-e9f59aaf39e9&script=Test-Create-ADObject.ps1&params=-jobId "789" -objectName "LINUX01" -objectDescription "Linux Server" -vmOS "Linux" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "Server"
```

### URL-Encoded Version (Recommended for Web Clients)
```
https://localhost:5001/webhook/v1?key=77aae8aa-50d2-49d9-be8c-e9f59aaf39e9&script=Test-Create-ADObject.ps1&params=-jobId%20%22123%22%20-objectName%20%22VM1%22%20-objectDescription%20%22Test%20VM%22%20-vmOS%20%22Windows%22%20-domain%20%22example.com%22%20-ouPath%20%22OU%3DTest%2CDC%3Dexample%2CDC%3Dcom%22%20-appType%20%22VDI%22
```

## POST Request Examples

### Basic Windows VM Test
```json
{
  "script": "Test-Create-ADObject.ps1",
  "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
  "params": "-jobId \"123\" -objectName \"VM1\" -objectDescription \"Test VM\" -vmOS \"Windows\" -domain \"example.com\" -ouPath \"OU=Test,DC=example,DC=com\" -appType \"VDI\""
}
```

### Cluster Test
```json
{
  "script": "Test-Create-ADObject.ps1",
  "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
  "params": "-jobId \"456\" -objectName \"CLS001\" -objectDescription \"Cluster VM\" -vmOS \"Windows\" -domain \"internal.com\" -ouPath \"OU=Clusters,DC=internal,DC=com\" -appType \"Cluster\" -clusterNodes \"NODE01,NODE02\""
}
```

### Linux VM Test
```json
{
  "script": "Test-Create-ADObject.ps1",
  "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
  "params": "-jobId \"789\" -objectName \"LINUX01\" -objectDescription \"Linux Server\" -vmOS \"Linux\" -domain \"example.com\" -ouPath \"OU=Test,DC=example,DC=com\" -appType \"Server\""
}
```

### Test Existing Object Scenario
```json
{
  "script": "Test-Create-ADObject.ps1",
  "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
  "params": "-jobId \"999\" -objectName \"VM2\" -objectDescription \"Test VM\" -vmOS \"Windows\" -domain \"example.com\" -ouPath \"OU=Test,DC=example,DC=com\" -appType \"VDI\" -simulateExisting"
}
```

### Test Error Scenario
```json
{
  "script": "Test-Create-ADObject.ps1",
  "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
  "params": "-jobId \"888\" -objectName \"VM3\" -objectDescription \"Test VM\" -vmOS \"Windows\" -domain \"example.com\" -ouPath \"OU=Test,DC=example,DC=com\" -appType \"VDI\" -simulateError"
}
```

## Curl Examples

### GET Request
```bash
curl "https://localhost:5001/webhook/v1?key=77aae8aa-50d2-49d9-be8c-e9f59aaf39e9&script=Test-Create-ADObject.ps1&params=-jobId%20%22123%22%20-objectName%20%22VM1%22%20-objectDescription%20%22Test%20VM%22%20-vmOS%20%22Windows%22%20-domain%20%22example.com%22%20-ouPath%20%22OU%3DTest%2CDC%3Dexample%2CDC%3Dcom%22%20-appType%20%22VDI%22"
```

### POST Request
```bash
curl -X POST https://localhost:5001/webhook/v1 \
  -H "Content-Type: application/json" \
  -d '{
    "script": "Test-Create-ADObject.ps1",
    "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
    "params": "-jobId \"123\" -objectName \"VM1\" -objectDescription \"Test VM\" -vmOS \"Windows\" -domain \"example.com\" -ouPath \"OU=Test,DC=example,DC=com\" -appType \"VDI\""
  }'
```

### POST Request with Cluster Nodes
```bash
curl -X POST https://localhost:5001/webhook/v1 \
  -H "Content-Type: application/json" \
  -d '{
    "script": "Test-Create-ADObject.ps1",
    "key": "77aae8aa-50d2-49d9-be8c-e9f59aaf39e9",
    "params": "-jobId \"456\" -objectName \"CLS001\" -objectDescription \"Cluster VM\" -vmOS \"Windows\" -domain \"internal.com\" -ouPath \"OU=Clusters,DC=internal,DC=com\" -appType \"Cluster\" -clusterNodes \"NODE01,NODE02\""
  }'
```

## Expected Response Format

All requests will return a JSON response in this format:

### Success Response
```json
{
  "scriptName": "Test-Create-ADObject.ps1",
  "params": "-jobId \"123\" -objectName \"VM1\" ...",
  "output": "{\"success\":\"true\",\"status\":\"COMPLETED\",\"message\":\"AD Object VM1 created successfully in example.com (SIMULATED)\",\"data\":{\"objectName\":\"VM1\",\"domain\":\"example.com\",\"comment\":\"AD Object VM1 created successfully in example.com (SIMULATED)\",\"ouCreatedIn\":\"OU=Test,DC=example,DC=com\",\"timeStamp\":\"2025-01-22 10:30:45\"}}"
}
```

### Error Response
```json
{
  "scriptName": "Test-Create-ADObject.ps1",
  "params": "-jobId \"888\" -objectName \"VM3\" ...",
  "output": "{\"success\":\"false\",\"status\":\"ERROR\",\"message\":\"Simulated error: Simulated error condition for testing\",\"data\":{\"objectName\":\"VM3\",\"domain\":\"example.com\",\"comment\":\"Simulated error: Simulated error condition for testing\",\"ouCreatedIn\":\"OU=Test,DC=example,DC=com\",\"timeStamp\":\"2025-01-22 10:30:45\"}}"
}
```

## Parameter Reference

| Parameter | Required | Description | Example Values |
|-----------|----------|-------------|----------------|
| `jobId` | Yes | Unique job identifier | "123", "456" |
| `objectName` | Yes | VM or cluster name | "VM1", "CLS001" |
| `objectDescription` | Yes | Description of the object | "Test VM", "Cluster VM" |
| `vmOS` | Yes | Operating system | "Windows", "Linux" |
| `domain` | Yes | Domain name | "example.com", "internal.com" |
| `ouPath` | Yes | Organizational Unit path | "OU=Test,DC=example,DC=com" |
| `appType` | Yes | Application type | "VDI", "Cluster", "Server" |
| `clusterNodes` | No | Comma-separated cluster nodes | "NODE01,NODE02" |
| `simulateExisting` | No | Test existing object scenario | (switch parameter) |
| `simulateError` | No | Test error scenario | (switch parameter) |

## Notes

- Use POST method for complex parameters with quotes and spaces
- URL-encode GET request parameters properly
- The script simulates all AD operations and doesn't require actual AD connectivity
- Linux VMs will skip AD object creation and return a completed status
- Cluster objects (CLS*/LST*) will simulate permission grants to specified nodes
