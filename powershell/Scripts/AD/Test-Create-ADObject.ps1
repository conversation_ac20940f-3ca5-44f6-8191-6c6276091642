<#
.SYNOPSIS
    Test script for Create-ADObject.ps1 that simulates AD Computer Account creation without performing actual operations.

.DESCRIPTION
    This script takes the same parameters as Create-ADObject.ps1 and follows the same logic flow,
    but simulates all AD operations for testing purposes. It validates input parameters and returns
    the same JSON output format without making any actual changes to Active Directory.

.PARAMETER JobID
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER Domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    Comma-separated list of cluster nodes for CLS/LST objects.

.PARAMETER SimulateExisting
    Simulate that the AD object already exists (for testing warning scenarios).

.PARAMETER SimulateError
    Simulate an error condition (for testing error scenarios).

.EXAMPLE
    .\Test-Create-ADObject.ps1 -JobID "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -Domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.EXAMPLE
    .\Test-Create-ADObject.ps1 -JobID "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -Domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"

.EXAMPLE
    .\Test-Create-ADObject.ps1 -JobID "789" -objectName "VM2" -objectDescription "Test VM" -vmOS "Windows" -Domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI" -SimulateExisting

.NOTES
    File Name   : Test-Create-ADObject.ps1
    Author      : Test Script
    Purpose     : Testing input/output for Create-ADObject.ps1
    
    This script simulates all AD operations and does not require:
    - ActiveDirectory module
    - CredentialManager module
    - Domain connectivity
    - Actual credentials
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$JobID,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ObjectName,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ObjectDescription,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$VmOS,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$Domain,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$OuPath,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$AppType,
    
    [Parameter(Mandatory = $false)]
    [string]$ClusterNodes,
    
    [Parameter(Mandatory = $false)]
    [switch]$SimulateExisting,
    
    [Parameter(Mandatory = $false)]
    [switch]$SimulateError
)

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet("Info", "Warning", "Error")]
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "Info" { Write-Host $logMessage -ForegroundColor Cyan }
        "Warning" { Write-Warning $logMessage }
        "Error" { Write-Error $logMessage }
    }
}

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    
    $objectReturn = @{
        objectName   = $ObjectName
        Domain       = $Domain
        Comment      = $Script:UpdateComment
        ouCreatedIn  = $OuPath
        timeStamp    = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ObjectName,
        
        [Parameter(Mandatory = $true)]
        [string]$ClusterNodeList,
        
        [Parameter(Mandatory = $true)]
        [string]$DomainName
    )
    
    Write-Log "Object name starts with CLS/LST, simulating cluster node permissions grant..." -Level "Info"
    
    $nodeList = $ClusterNodeList -split ',' | ForEach-Object { $_.Trim() }
    
    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            Write-Log "Simulating permission grant to $node on $ObjectName" -Level "Info"
        }
    }
}

try {
    $Script:UpdateComment = ""
    $jobStatus = "IN_PROGRESS"
    $success = $true
    $accountExists = $false

    Write-Log "Starting AD Object creation test for JobID: $JobID" -Level "Info"
    Write-Log "Parameters: ObjectName=$ObjectName, VmOS=$VmOS, Domain=$Domain, AppType=$AppType" -Level "Info"

    # Simulate error condition if requested
    if ($SimulateError) {
        throw "Simulated error condition for testing"
    }

    # Handle Linux OS case
    if ($VmOS -eq "Linux") {
        Write-Log "This is a Linux server - skipping AD account creation" -Level "Info"
        $Script:UpdateComment = "Linux based server, no AD Object created."
        $jobStatus = "COMPLETED"
        return (New-JsonReturn -Success $success -Status $jobStatus -Message $Script:UpdateComment)
    }

    # Simulate credential retrieval
    Write-Log "Simulating credential retrieval for domain: $Domain" -Level "Info"
    $credentialTarget = if ($AppType -eq "VDI") { "VDI" } else { $Domain }
    Write-Log "Using credential target: $credentialTarget" -Level "Info"

    # Simulate checking for existing AD object
    Write-Log "Simulating check for existing computer object '$ObjectName' in domain '$Domain'..." -Level "Info"
    
    if ($SimulateExisting) {
        $warningMessage = "$ObjectName already exists in the $Domain domain; skipping the creation step."
        Write-Log $warningMessage -Level "Warning"
        return (New-JsonReturn -Success "false" -Status "WARNING" -Message $warningMessage)
    }
    
    Write-Log "Computer name '$ObjectName' not found in the '$Domain' domain; proceeding with object creation." -Level "Info"
    $accountExists = $false

    # Simulate AD object creation
    if (-not $accountExists) {
        Write-Log "Simulating AD computer object creation '$ObjectName' in domain '$Domain'..." -Level "Info"
        
        # Simulate the New-ADComputer parameters
        $simulatedParams = @{
            Name            = $ObjectName
            SAMAccountName  = $ObjectName
            Path            = $OuPath
            Description     = $ObjectDescription
            Enabled         = $true
            DNSHostName     = "$ObjectName.$Domain"
            Comment         = $JobID
        }
        
        Write-Log "Simulated AD Computer parameters: $($simulatedParams | ConvertTo-Json -Compress)" -Level "Info"
        Write-Log "Successfully simulated AD computer object creation '$ObjectName'" -Level "Info"

        # Simulate AD replication wait
        Write-Log "Simulating AD replication wait..." -Level "Info"
        Start-Sleep -Seconds 2  # Reduced for testing
    }

    # Simulate cluster node permissions for CLS/LST objects
    if (($ObjectName.StartsWith("CLS", [System.StringComparison]::OrdinalIgnoreCase) -or 
         $ObjectName.StartsWith("LST", [System.StringComparison]::OrdinalIgnoreCase)) -and 
         -not [string]::IsNullOrWhiteSpace($ClusterNodes)) {
        
        Test-ClusterNodePermissions -ObjectName $ObjectName -ClusterNodeList $ClusterNodes -DomainName $Domain
    }
    
    $Script:UpdateComment = "AD Object $ObjectName created successfully in $Domain (SIMULATED)"
    $jobStatus = "COMPLETED"
    Write-Log "AD object '$ObjectName' created successfully in the '$Domain' domain (SIMULATED)" -Level "Info"
}
catch {
    $errorMessage = "Simulated error: $($_.Exception.Message)"
    Write-Log $errorMessage -Level "Error"
    return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
}
finally {
    if (-not [string]::IsNullOrEmpty($Script:UpdateComment)) {
        $result = New-JsonReturn -Success $success -Status $jobStatus -Message $Script:UpdateComment
        Write-Log "Final JSON Response:" -Level "Info"
        Write-Host $result -ForegroundColor Green
        return $result
    }
}
