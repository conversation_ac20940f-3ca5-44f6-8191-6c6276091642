<#
.SYNOPSIS
    Test script for Create-ADObject.ps1 that simulates AD Computer Account creation without performing actual operations.

.DESCRIPTION
    This script takes the same parameters as Create-ADObject.ps1 and follows the same logic flow,
    but simulates all AD operations for testing purposes. It validates input parameters and returns
    the same JSON output format without making any actual changes to Active Directory.

.PARAMETER jobId
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    Comma-separated list of cluster nodes for CLS/LST objects.

.PARAMETER simulateExisting
    Simulate that the AD object already exists (for testing warning scenarios).

.PARAMETER simulateError
    Simulate an error condition (for testing error scenarios).

.EXAMPLE
    .\Test-Create-ADObject.ps1 -jobId "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.EXAMPLE
    .\Test-Create-ADObject.ps1 -jobId "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"

.EXAMPLE
    .\Test-Create-ADObject.ps1 -jobId "789" -objectName "VM2" -objectDescription "Test VM" -vmOS "Windows" -domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI" -simulateExisting

.NOTES
    File Name   : Test-Create-ADObject.ps1
    Author      : Test Script
    Purpose     : Testing input/output for Create-ADObject.ps1
    
    This script simulates all AD operations and does not require:
    - ActiveDirectory module
    - CredentialManager module
    - Domain connectivity
    - Actual credentials
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$jobId,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectName,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$objectDescription,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$vmOS,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$domain,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ouPath,

    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$appType,

    [Parameter(Mandatory = $false)]
    [string]$clusterNodes,

    [Parameter(Mandatory = $false)]
    [switch]$simulateExisting,

    [Parameter(Mandatory = $false)]
    [switch]$simulateError
)

function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$message,

        [Parameter(Mandatory = $false)]
        [ValidateSet("Info", "Warning", "Error")]
        [string]$level = "Info"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$level] $message"

    switch ($level) {
        "Info" { Write-Host $logMessage -ForegroundColor Cyan }
        "Warning" { Write-Warning $logMessage }
        "Error" { Write-Error $logMessage }
    }
}

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,

        [Parameter(Mandatory = $true)]
        [string]$status,

        [Parameter(Mandatory = $true)]
        [string]$message
    )

    $objectReturn = @{
        objectName   = $objectName
        domain       = $domain
        comment      = $Script:updateComment
        ouCreatedIn  = $ouPath
        timeStamp    = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }

    $jsonResponse = @{
        success = $success
        status  = $status
        message = $message
        data    = $objectReturn
    }

    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Test-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$objectName,

        [Parameter(Mandatory = $true)]
        [string]$clusterNodeList,

        [Parameter(Mandatory = $true)]
        [string]$domainName
    )

    Write-Log "Object name starts with CLS/LST, simulating cluster node permissions grant..." -level "Info"

    $nodeList = $clusterNodeList -split ',' | ForEach-Object { $_.Trim() }

    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            Write-Log "Simulating permission grant to $node on $objectName" -level "Info"
        }
    }
}

try {
    $Script:updateComment = ""
    $jobStatus = "IN_PROGRESS"
    $success = $true
    $accountExists = $false

    Write-Log "Starting AD Object creation test for jobId: $jobId" -level "Info"
    Write-Log "Parameters: objectName=$objectName, vmOS=$vmOS, domain=$domain, appType=$appType" -level "Info"

    # Simulate error condition if requested
    if ($simulateError) {
        throw "Simulated error condition for testing"
    }

    # Handle Linux OS case
    if ($vmOS -eq "Linux") {
        Write-Log "This is a Linux server - skipping AD account creation" -level "Info"
        $Script:updateComment = "Linux based server, no AD Object created."
        $jobStatus = "COMPLETED"
        return (New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment)
    }

    # Simulate credential retrieval
    Write-Log "Simulating credential retrieval for domain: $domain" -level "Info"
    $credentialTarget = if ($appType -eq "VDI") { "VDI" } else { $domain }
    Write-Log "Using credential target: $credentialTarget" -level "Info"

    # Simulate checking for existing AD object
    Write-Log "Simulating check for existing computer object '$objectName' in domain '$domain'..." -level "Info"

    if ($simulateExisting) {
        $warningMessage = "$objectName already exists in the $domain domain; skipping the creation step."
        Write-Log $warningMessage -level "Warning"
        return (New-JsonReturn -success "false" -status "WARNING" -message $warningMessage)
    }

    Write-Log "Computer name '$objectName' not found in the '$domain' domain; proceeding with object creation." -level "Info"
    $accountExists = $false

    # Simulate AD object creation
    if (-not $accountExists) {
        Write-Log "Simulating AD computer object creation '$objectName' in domain '$domain'..." -level "Info"

        # Simulate the New-ADComputer parameters
        $simulatedParams = @{
            name            = $objectName
            samAccountName  = $objectName
            path            = $ouPath
            description     = $objectDescription
            enabled         = $true
            dnsHostName     = "$objectName.$domain"
            comment         = $jobId
        }

        Write-Log "Simulated AD Computer parameters: $($simulatedParams | ConvertTo-Json -Compress)" -level "Info"
        Write-Log "Successfully simulated AD computer object creation '$objectName'" -level "Info"

        # Simulate AD replication wait
        Write-Log "Simulating AD replication wait..." -level "Info"
        Start-Sleep -Seconds 2  # Reduced for testing
    }

    # Simulate cluster node permissions for CLS/LST objects
    if (($objectName.StartsWith("CLS", [System.StringComparison]::OrdinalIgnoreCase) -or
         $objectName.StartsWith("LST", [System.StringComparison]::OrdinalIgnoreCase)) -and
         -not [string]::IsNullOrWhiteSpace($clusterNodes)) {

        Test-ClusterNodePermissions -objectName $objectName -clusterNodeList $clusterNodes -domainName $domain
    }

    $Script:updateComment = "AD Object $objectName created successfully in $domain (SIMULATED)"
    $jobStatus = "COMPLETED"
    Write-Log "AD object '$objectName' created successfully in the '$domain' domain (SIMULATED)" -level "Info"
}
catch {
    $errorMessage = "Simulated error: $($_.Exception.Message)"
    Write-Log $errorMessage -level "Error"
    return (New-JsonReturn -success "false" -status "ERROR" -message $errorMessage)
}

# Generate final JSON response
if (-not [string]::IsNullOrEmpty($Script:updateComment)) {
    $result = New-JsonReturn -success $success -status $jobStatus -message $Script:updateComment
    Write-Log "Final JSON Response:" -level "Info"
    Write-Host $result -ForegroundColor Green
    return $result
}
